const express = require('express');
const { check } = require('express-validator');
const {
  getAdminSettings,
  updateAdminSettings
} = require('../../controllers/admin/settings');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// All admin settings routes are protected and admin-only
router.use(protect);
router.use(authorize('admin'));

// Get platform settings
router.get('/', getAdminSettings);

// Update platform settings
router.put('/', [
  // General settings validation
  check('general.siteName', 'Site name must be a string').optional().isString(),
  check('general.siteLogo', 'Site logo must be a string').optional().isString(),
  check('general.contactEmail', 'Contact email must be valid').optional().isEmail(),
  check('general.contactPhone', 'Contact phone must be a string').optional().isString(),
  check('general.address', 'Address must be a string').optional().isString(),
  check('general.supportLink', 'Support link must be a valid URL').optional().isURL(),
  check('general.maintenanceMode', 'Maintenance mode must be boolean').optional().isBoolean(),
  
  // Financial settings validation
  check('financial.platformCommissionPercentage', 'Platform commission must be a number between 0 and 100')
    .optional()
    .isFloat({ min: 0, max: 100 })
], updateAdminSettings);

module.exports = router;
