const ErrorResponse = require("../../utils/errorResponse");
const Setting = require("../../models/Setting");
const { validationResult } = require("express-validator");

// @desc    Get platform settings
// @route   GET /api/admin/settings
// @access  Private/Admin
exports.getAdminSettings = async (req, res, next) => {
  try {
    const settings = await Setting.getSingleton();

    res.status(200).json({
      success: true,
      data: settings,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update platform settings
// @route   PUT /api/admin/settings
// @access  Private/Admin
exports.updateAdminSettings = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        success: false, 
        errors: errors.array() 
      });
    }

    // Get the singleton settings document
    let settings = await Setting.getSingleton();

    // Update fields that are provided in the request
    if (req.body.general) {
      Object.keys(req.body.general).forEach(key => {
        if (req.body.general[key] !== undefined) {
          settings.general[key] = req.body.general[key];
        }
      });
    }

    if (req.body.financial) {
      Object.keys(req.body.financial).forEach(key => {
        if (req.body.financial[key] !== undefined) {
          settings.financial[key] = req.body.financial[key];
        }
      });
    }

    // Set updatedBy field
    settings.updatedBy = req.user.id;

    // Save the updated settings
    await settings.save();

    res.status(200).json({
      success: true,
      data: settings,
    });
  } catch (err) {
    next(err);
  }
};
